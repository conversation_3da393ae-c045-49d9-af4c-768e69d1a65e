using System;
using LitJson;
using UnityEngine;
[Serializable]
public class InfoGate : ConfigInfoBase
{
    public int gate;
    // public int majiangTypeCount;
    public int majiangCount;
    public int topTypeCount;
    public int topCount;
    public int gameTime;
    public string percentDesc;
    public bool isHard;
    public int lockSlotCount;
    public int chiCount;
    public bool hasInterstitialAd;
    public int majiangNumType;
    public int[] majiangNumRange;
    public override object GetKey(int index)
    {
        return gate;
    }

    public override void Parse(JsonData json)
    {
        gate = JsonUtil.ToInt(json, "gate");
        // majiangTypeCount = JsonUtil.ToInt(json, "majiangTypeCount");
        majiangCount = JsonUtil.ToInt(json, "majiangCount");
        topTypeCount = JsonUtil.ToInt(json, "topTypeCount");
        topCount = JsonUtil.ToInt(json, "topCount");
        gameTime = JsonUtil.ToInt(json, "gameTime");
        percentDesc = JsonUtil.ToString(json, "percentDesc");
        isHard = JsonUtil.ToInt(json, "isHard") == 1;
        lockSlotCount = JsonUtil.ToInt(json, "lockSlotCount");
        chiCount = JsonUtil.ToInt(json, "chiCount");
        hasInterstitialAd = JsonUtil.ToInt(json, "hasInterstitialAd") == 1;
        majiangNumType = JsonUtil.ToInt(json, "majiangNumType");
        majiangNumRange = JsonUtil.ToIntArr(json, "majiangNumRange");
#if UNITY_EDITOR
        if (majiangCount % 2 != 0)
        {
            Debug.LogError($"总数不是2的倍数 gate:{gate} majiangCount:{majiangCount}  fix:{Mathf.FloorToInt(majiangCount / 2f) * 2}");
        }
        if (topCount % 2 != 0)
        {
            Debug.LogError($"总数不是2的倍数 gate:{gate} topCount:{topCount}  fix:{Mathf.FloorToInt(topCount / 2f) * 2}");
        }
#endif
    }
}