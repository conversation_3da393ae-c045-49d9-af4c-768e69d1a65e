using UnityEngine;
using System.Collections.Generic;

public class MajiangGeneratorTest : MonoBehaviour
{
    [ContextMenu("测试麻将生成器配置")]
    public void TestMajiangGeneratorConfig()
    {
        Debug.Log("开始测试麻将生成器配置...");
        
        // 测试Gate 1: majiangNumType=2, majiangNumRange=[1,3] (有效配置)
        TestGateConfig(1);
        
        // 测试Gate 2: majiangNumType=5, majiangNumRange=[1,3] (无效配置，应该报错)
        TestGateConfig(2);
        
        // 测试Gate 3: majiangNumType=3, majiangNumRange=[1,4] (有效配置)
        TestGateConfig(3);
    }
    
    private void TestGateConfig(int gateId)
    {
        Debug.Log($"=== 测试Gate {gateId} ===");
        
        var gateInfo = ConfigGate.GetData(gateId);
        if (gateInfo == null)
        {
            Debug.LogError($"无法获取Gate {gateId}的配置");
            return;
        }
        
        Debug.Log($"Gate {gateId} 配置:");
        Debug.Log($"  majiangNumType: {gateInfo.majiangNumType}");
        Debug.Log($"  majiangNumRange: [{string.Join(", ", gateInfo.majiangNumRange)}]");
        
        // 验证配置
        if (gateInfo.majiangNumRange != null && gateInfo.majiangNumRange.Length == 2)
        {
            int rangeStart = gateInfo.majiangNumRange[0];
            int rangeEnd = gateInfo.majiangNumRange[1];
            int availableTypeCount = rangeEnd - rangeStart + 1;
            
            Debug.Log($"  可用类型范围: {rangeStart} 到 {rangeEnd} (共{availableTypeCount}种)");
            
            if (gateInfo.majiangNumType > availableTypeCount)
            {
                Debug.LogError($"  配置无效: 需要{gateInfo.majiangNumType}种类型，但只有{availableTypeCount}种可用");
                return;
            }
            else
            {
                Debug.Log($"  配置有效: 需要{gateInfo.majiangNumType}种类型，有{availableTypeCount}种可用");
            }
        }
        
        // 测试生成麻将
        try
        {
            var majiangs = MajiangGenerator.Inst.Generate(gateInfo);
            Debug.Log($"  成功生成{majiangs.Count}个麻将");
            
            // 统计生成的麻将类型
            var typeCount = new Dictionary<MaJiangType, int>();
            foreach (var majiang in majiangs)
            {
                if (!typeCount.ContainsKey(majiang.type))
                    typeCount[majiang.type] = 0;
                typeCount[majiang.type]++;
            }
            
            Debug.Log("  生成的麻将类型统计:");
            foreach (var kvp in typeCount)
            {
                Debug.Log($"    {kvp.Key}: {kvp.Value}个");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"  生成麻将时出错: {e.Message}");
        }
        
        Debug.Log("");
    }
}
