﻿public class ConfigGate : ConfigBase
{
    private static InfoGate lastGateInfo;
    internal override void CacheData(object key, ConfigInfoBase info)
    {
        base.CacheData(key, info);
        lastGateInfo = info as InfoGate;
#if UNITY_EDITOR
        if (lastGateInfo.majiangCount % 2 != 0)
        {
            UnityEngine.Debug.LogError("总数不是2的倍数");
            return;
        }

        // 验证麻将数字类型配置
        if (lastGateInfo.majiangNumRange != null && lastGateInfo.majiangNumRange.Length == 2)
        {
            int rangeStart = lastGateInfo.majiangNumRange[0];
            int rangeEnd = lastGateInfo.majiangNumRange[1];
            int availableTypeCount = rangeEnd - rangeStart + 1;

            if (lastGateInfo.majiangNumType > availableTypeCount)
            {
                UnityEngine.Debug.LogError($"麻将数字类型配置无效：majiangNumType({lastGateInfo.majiangNumType}) 超过可用范围数量({availableTypeCount})，范围为[{rangeStart}, {rangeEnd}]");
                return;
            }
        }
        else if (lastGateInfo.majiangNumType > 0)
        {
            UnityEngine.Debug.LogError("麻将数字类型范围配置无效：majiangNumRange必须是包含两个元素的数组[开始类型, 结束类型]");
            return;
        }
#endif
    }
    public static InfoGate GetData(int level)
    {
        var info = ConfigManager.GetConfig<ConfigGate>().GetData<InfoGate>(level);
        if (info == null)
        {
            info = lastGateInfo;
        }
        return info;
    }
}