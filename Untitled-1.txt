
> Configure project :launcher
WARNING:The option setting 'android.bundle.enableUncompressedNativeLibs=false' is deprecated.
The current default is 'true'.
It will be removed in version 8.0 of the Android Gradle plugin.
You can add the following to your build.gradle instead:
android {
    packagingOptions {
        jniLibs {
            useLegacyPackaging = true
        }
    }
}
WARNING:The option setting 'android.aapt2FromMavenOverride=F:\AndroidEnv\AndroidPlayer-bobo-tw\SDK\build-tools\35.0.0\aapt2.exe' is experimental.
WARNING:Using flatDir should be avoided because it doesn't support any meta-data formats.

> Configure project :unityLibrary
WARNING:Using flatDir should be avoided because it doesn't support any meta-data formats.

> Configure project :unityLibrary:FirebaseApp.androidlib
WARNING:Using flatDir should be avoided because it doesn't support any meta-data formats.
WARNING:We recommend using a newer Android Gradle plugin to use compileSdk = 35

This Android Gradle plugin (7.2.0) was tested up to compileSdk = 32

This warning can be suppressed by adding
    android.suppressUnsupportedCompileSdk=35
to this project's gradle.properties

The build will continue, but you are strongly encouraged to update your project to
use a newer Android Gradle Plugin that has been tested with compileSdk = 35

> Task :unityLibrary:preBuild UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:preBuild UP-TO-DATE
> Task :launcher:preBuild UP-TO-DATE
> Task :launcher:preReleaseBuild UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:preReleaseBuild UP-TO-DATE
> Task :unityLibrary:preReleaseBuild UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseRenderscript NO-SOURCE
> Task :unityLibrary:packageReleaseRenderscript NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseRenderscript NO-SOURCE
> Task :launcher:generateReleaseResValues
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseResValues
> Task :unityLibrary:generateReleaseResValues
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseResources
> Task :launcher:compileReleaseRenderscript NO-SOURCE
> Task :unityLibrary:compileReleaseRenderscript NO-SOURCE
> Task :launcher:generateReleaseResources
> Task :unityLibrary:generateReleaseResources
> Task :launcher:createReleaseCompatibleScreenManifests
> Task :launcher:extractDeepLinksRelease
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseResources
> Task :unityLibrary:FirebaseApp.androidlib:extractDeepLinksRelease
> Task :unityLibrary:packageReleaseResources
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseLibraryResources
> Task :unityLibrary:extractDeepLinksRelease
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseAidl NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:writeReleaseAarMetadata
> Task :unityLibrary:compileReleaseAidl NO-SOURCE
> Task :unityLibrary:writeReleaseAarMetadata
> Task :unityLibrary:compileReleaseLibraryResources
> Task :unityLibrary:FirebaseApp.androidlib:processReleaseManifest
> Task :unityLibrary:generateReleaseBuildConfig
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseBuildConfig
> Task :unityLibrary:javaPreCompileRelease
> Task :unityLibrary:FirebaseApp.androidlib:parseReleaseLocalResources

> Task :unityLibrary:processReleaseManifest
E:\ProjectsUnity\MaJiangClassic-zplay\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:35:5-40 Warning:
	property was tagged at AndroidManifest.xml:35 to remove other declarations but no other declaration present

> Task :unityLibrary:FirebaseApp.androidlib:javaPreCompileRelease
> Task :unityLibrary:parseReleaseLocalResources
> Task :unityLibrary:mergeReleaseShaders
> Task :unityLibrary:compileReleaseShaders NO-SOURCE
> Task :unityLibrary:generateReleaseAssets UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseShaders
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseShaders NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseAssets UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:packageReleaseAssets
> Task :unityLibrary:FirebaseApp.androidlib:processReleaseJavaRes NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibResRelease NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseJniLibFolders
> Task :unityLibrary:FirebaseApp.androidlib:mergeReleaseNativeLibs NO-SOURCE
> Task :unityLibrary:FirebaseApp.androidlib:prepareReleaseArtProfile UP-TO-DATE
> Task :unityLibrary:FirebaseApp.androidlib:copyReleaseJniLibsProjectOnly
> Task :unityLibrary:packageReleaseAssets
> Task :unityLibrary:processReleaseJavaRes NO-SOURCE
> Task :unityLibrary:bundleLibResRelease NO-SOURCE
> Task :unityLibrary:mergeReleaseJniLibFolders
> Task :unityLibrary:FirebaseApp.androidlib:generateReleaseRFile
> Task :unityLibrary:prepareReleaseArtProfile UP-TO-DATE
> Task :unityLibrary:mergeReleaseNativeLibs
> Task :unityLibrary:FirebaseApp.androidlib:compileReleaseJavaWithJavac
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibRuntimeToJarRelease
> Task :unityLibrary:FirebaseApp.androidlib:bundleLibCompileToJarRelease
> Task :unityLibrary:copyReleaseJniLibsProjectOnly
> Task :launcher:checkReleaseDuplicateClasses
> Task :unityLibrary:generateReleaseRFile

> Task :unityLibrary:compileReleaseJavaWithJavac

> Task :launcher:mergeReleaseResources

> Task :launcher:processReleaseMainManifest
[com.pangle.global:pag-sdk:7.1.0.8] C:\Users\<USER>\.gradle\caches\transforms-3\f65595761cf64f64f7d4d94edab0a69b\transformed\jetified-pag-sdk-7.1.0.8\AndroidManifest.xml Warning:
	Namespace 'com.bytedance' used in: com.pangle.global:pag-sdk:7.1.0.8, com.pangle.global:pag-sdk-ad:unfat-7108-20250429200228.
E:\ProjectsUnity\MaJiangClassic-zplay\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:98:9-44 Warning:
	property was tagged at AndroidManifest.xml:98 to remove other declarations but no other declaration present

> Task :launcher:processReleaseManifest
> Task :launcher:processApplicationManifestReleaseForBundle
> Task :launcher:compileReleaseAidl NO-SOURCE
> Task :launcher:generateReleaseBuildConfig
> Task :launcher:javaPreCompileRelease
> Task :launcher:mergeReleaseShaders
> Task :launcher:compileReleaseShaders NO-SOURCE
> Task :launcher:generateReleaseAssets UP-TO-DATE
> Task :launcher:checkReleaseAarMetadata
> Task :launcher:mergeReleaseAssets
> Task :launcher:processReleaseJavaRes NO-SOURCE
> Task :launcher:mergeReleaseJniLibFolders
> Task :launcher:writeReleaseAppMetadata
> Task :launcher:mergeReleaseNativeLibs
> Task :unityLibrary:bundleLibCompileToJarRelease
> Task :unityLibrary:bundleLibRuntimeToJarRelease
> Task :launcher:mergeReleaseJavaResource
> Task :launcher:collectReleaseDependencies
> Task :launcher:mapReleaseSourceSetPaths

> Task :launcher:stripReleaseDebugSymbols
[CXX5106] NDK was located by using ndk.dir property. This method is deprecated and will be removed in a future release. Please use android.ndkVersion or android.ndkPath in build.gradle to specify the NDK to use. https://developer.android.com/r/studio-ui/ndk-dir

> Task :launcher:configureReleaseDependencies
> Task :launcher:parseReleaseIntegrityConfig UP-TO-DATE
> Task :launcher:validateSigningRelease
> Task :launcher:processReleaseManifestForPackage
> Task :launcher:mergeReleaseArtProfile

> Task :launcher:extractReleaseNativeSymbolTables
[CXX5106] NDK was located by using ndk.dir property. This method is deprecated and will be removed in a future release. Please use android.ndkVersion or android.ndkPath in build.gradle to specify the NDK to use. https://developer.android.com/r/studio-ui/ndk-dir

> Task :launcher:bundleReleaseResources
> Task :launcher:processReleaseResources
> Task :launcher:compileReleaseJavaWithJavac

> Task :launcher:dexBuilderRelease

> Task :launcher:dexBuilderRelease FAILED

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

See https://docs.gradle.org/7.4.2/userguide/command_line_interface.html#sec:command_line_warnings
67 actionable tasks: 64 executed, 3 up-to-date

UnityEngine.GUIUtility:ProcessEvent (int,intptr,bool&)
