# 麻将生成器配置说明

## 新增配置字段

在InfoGate配置系统中新增了以下两个字段来控制麻将生成器的行为：

### 1. majiangNumType (int)
- **描述**: 麻将数字类型数量
- **作用**: 指定要生成的不同数字类型的数量
- **示例**: 如果设置为2，则只会使用2种不同的麻将类型

### 2. majiangNumRange (int[])
- **描述**: 麻将数字类型范围
- **格式**: [开始类型, 结束类型]
- **作用**: 定义可用的数字类型范围
- **示例**: [1, 3] 表示可以使用类型1到类型3（即万、筒、条）

## 麻将类型枚举值
```csharp
public enum MaJiangType
{
    Wan = 1,    // 万
    Tong = 2,   // 筒
    Tiao = 3,   // 条
    Dapai = 4,  // 大牌
    Item = 5,   // 道具
}
```

## 配置验证规则

系统会自动验证配置的有效性：

1. **范围检查**: `majiangNumType` 必须小于等于 `majiangNumRange` 范围的总数量
   - 计算公式: 结束类型 - 开始类型 + 1
   - 例如: 范围[1,3]的总数量为 3-1+1=3

2. **格式检查**: `majiangNumRange` 必须是包含两个元素的数组

3. **错误处理**: 如果配置无效，会在编辑器中输出错误日志

## 配置示例

### 有效配置示例
```json
{
  "gate": 1,
  "majiangCount": 6,
  "majiangNumType": 2,
  "majiangNumRange": [1, 3]
}
```
- 可用类型范围: 1到3 (万、筒、条)
- 实际使用类型数量: 2种
- 结果: 有效，会从万、筒、条中随机选择2种类型

### 无效配置示例
```json
{
  "gate": 2,
  "majiangCount": 60,
  "majiangNumType": 5,
  "majiangNumRange": [1, 3]
}
```
- 可用类型范围: 1到3 (万、筒、条)，共3种
- 需要使用类型数量: 5种
- 结果: 无效，会输出错误日志

## 使用方法

1. **配置文件**: 在 `Excels/json/Gate.json` 中添加新字段
2. **代码调用**: MajiangGenerator会自动读取配置并应用
3. **测试**: 可以使用 `MajiangGeneratorTest` 脚本进行测试

## 测试方法

1. 在Unity编辑器中找到包含 `MajiangGeneratorTest` 组件的GameObject
2. 在Inspector面板中右键点击组件
3. 选择 "测试麻将生成器配置" 菜单项
4. 查看Console窗口的输出结果

## 注意事项

1. 如果不设置这两个新字段，系统会使用默认行为（使用所有基础类型）
2. 配置验证只在编辑器模式下进行，发布版本不会进行验证
3. 建议在修改配置后进行测试，确保生成的麻将符合预期
